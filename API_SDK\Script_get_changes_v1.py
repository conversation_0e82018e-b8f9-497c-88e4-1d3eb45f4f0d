#Python version - 3.8
#This script requires requests module installed in python.
from urllib.error import HTTPError
from urllib.parse import urlencode
from urllib.request import urlopen,Request

url = "https://servicedesk.grupoficohsa.hn:8443/app/Changes.cc/api/v3/changes/{18680}"
headers ={"Accept": "application/vnd.manageengine.sdp.v3+json", 
          "Authorization" : "Zoho-oauthtoken 79279DA3-0701-4051-BBC9-03CCF2F8C471", 
          "Content-Type" : "application/x-www-form-urlencoded"}
httprequest = Request(url, headers=headers)
try:
    with urlopen(httprequest) as response:
        print(response.read().decode())
except HTTPError as e:
    print(e.read().decode())
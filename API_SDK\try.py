
#Python version - 3.8

#Importar Librería requests
#python -m pip install requests


#This script requires requests module installed in python.
from urllib.error import HTTPError
from urllib.parse import urlencode
from urllib.request import urlopen,Request
import ssl

#Variable tipo long
id_cambio = 18680
token = "79279DA3-0701-4051-BBC9-03CCF2F8C471"


url = f"https://servicedesk.grupoficohsa.hn:8443/api/v3/changes/{id_cambio}"
headers ={"Accept": "application/vnd.manageengine.sdp.v3+json", 
          "Authorization" : f"authtoken: {token}", 
          "Content-Type" : "application/x-www-form-urlencoded"}
input_data = '''{}'''       
url += "?" + urlencode({"input_data":input_data})
httprequest = Request(url, headers=headers)

# Crear un contexto SSL que no verifique certificados (solo para servidores internos)
ssl_context = ssl.create_default_context()
ssl_context.check_hostname = False
ssl_context.verify_mode = ssl.CERT_NONE

try:
    with urlopen(httprequest, context=ssl_context) as response:
        print(response.read().decode())
except HTTPError as e:
    print(e.read().decode())
    
    

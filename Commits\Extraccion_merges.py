import requests
import datetime
import json
import re
import sys
from requests_oauthlib import OAuth1
import pandas as pd
from openpyxl import Workbook
from openpyxl.utils import get_column_letter
from openpyxl.styles import PatternFill
import calendar

now = datetime.datetime.now()  # Fecha y hora actual
date_time_format = now.strftime("%Y%m%d_%H%M%S")

print("Hora de inicio:" + now.strftime("%Y/%m/%d %H:%M:%S"))

# Solicitar el nombre del repositorio
repoName = input("Ingrese el nombre del repositorio: ")
print("repoName: " + repoName)
print("Largo:" + repr(len(repoName)))

# Solicitar el tipo de selección de fechas
print("\nSelección de rango de fechas:")
print("1. Mes y año específicos")
print("2. Rango de fechas personalizado")
print("3. Usar últimos 35 días (predeterminado)")
opcion = input("Seleccione una opción (1-3): ")

if opcion == "1":
    # Solicitar mes y año
    mes = int(input("Ingrese el mes (1-12): "))
    anio = int(input("Ingrese el año: "))
   
    # Calcular el primer y último día del mes
    primer_dia = datetime.datetime(anio, mes, 1)
    ultimo_dia = datetime.datetime(anio, mes, calendar.monthrange(anio, mes)[1])
    fecha_inicio = primer_dia
    fecha_fin = ultimo_dia
elif opcion == "2":
    # Solicitar fechas de inicio y fin
    fecha_inicio_str = input("Ingrese la fecha de inicio (formato: YYYY-MM-DD): ")
    fecha_fin_str = input("Ingrese la fecha de fin (formato: YYYY-MM-DD): ")
   
    fecha_inicio = datetime.datetime.strptime(fecha_inicio_str, '%Y-%m-%d')
    fecha_fin = datetime.datetime.strptime(fecha_fin_str, '%Y-%m-%d')
else:
    # Por defecto, usar los últimos 35 días
    fecha_fin = datetime.datetime.now()
    fecha_inicio = fecha_fin - datetime.timedelta(days=35)

print(f"Fecha de inicio: {fecha_inicio.strftime('%Y-%m-%d')}")
print(f"Fecha de fin: {fecha_fin.strftime('%Y-%m-%d')}")

consumer_key = 'h4MketHQ9gPHv57Cms'
consumer_secret = 'u6St6a6ARrgS23NjWY97Ps2MW9YP6MBA'

oauth = OAuth1(
    client_key=consumer_key,
    client_secret=consumer_secret
)

url = "https://api.bitbucket.org/2.0/repositories/ficohsacore/" + repoName + "/refs/tags"
print("URL: " + url)

headers = {
    "Accept": "application/json"
}

params = {'pagelen': 100}

# Inicializa la lista de los branchs
branchs = []

while url:
    response = requests.request("GET", url, headers=headers, auth=oauth, params=params)
    if response.status_code == 200:
        # Convertir la respuesta JSON en un diccionario de Python
        branchs_data = json.loads(response.text)
        # Agrega los branchs de la página actual a la lista de branchs
        branchs.extend(branchs_data["values"])
        # Actualiza la URL y los parámetros de la siguiente página, si están disponibles
        if "next" in branchs_data:
            url = branchs_data["next"]
            params = {}
        else:
            url = None
    else:
        print(f"Error al obtener la información de Bitbucket. Código de estado HTTP: {response.status_code}")
        break

# Calcular la fecha de filtro
fecha_filtro_inicio = fecha_inicio
fecha_filtro_fin = fecha_fin

data = {
    'Repositorio': [],
    'Fecha': [],
    'CC': [],
    'Autor': [],
    'Commit': [],
    'Tipo': [],
    'Ruta base': [],
    'Archivo con extensión': [],
    'Archivo': [],
    'Ext': []    
}

# Iterar sobre la lista de branchs y guardar los detalles en las listas
for branch in branchs:
    # Verificar si la rama tiene un tag y una fecha dentro del rango seleccionado
    if 'name' in branch and branch['name']:
        # Extraer fecha del tag si tiene el formato YYYY.MM.DD
        tag_fecha_search = re.search(r'(\d{4})\.(\d{2})\.(\d{2})', branch['name'])
        if tag_fecha_search:
            # Convertir la fecha del tag al formato YYYY-MM-DD
            year, month, day = tag_fecha_search.groups()
            try:
                # Validar que los valores sean números válidos para una fecha
                year_int, month_int, day_int = int(year), int(month), int(day)
                if 1 <= month_int <= 12:  # Validar que el mes esté entre 1 y 12
                    fecha_tag = datetime.datetime(year_int, month_int, day_int)
                    fecha_branch = fecha_tag
                else:
                    # Si el mes no es válido, usar la fecha del commit
                    fecha_branch = datetime.datetime.strptime(branch['target']['date'], '%Y-%m-%dT%H:%M:%S+00:00')
            except (ValueError, OverflowError):
                # Si hay algún error al crear la fecha, usar la fecha del commit
                fecha_branch = datetime.datetime.strptime(branch['target']['date'], '%Y-%m-%dT%H:%M:%S+00:00')
        else:
            # Si no se encuentra fecha en el tag, usar la fecha del commit
            fecha_branch = datetime.datetime.strptime(branch['target']['date'], '%Y-%m-%dT%H:%M:%S+00:00')
            
        if fecha_filtro_inicio <= fecha_branch <= fecha_filtro_fin:
            commit_id = branch['target']['hash']
            tag_code_search = re.search(r'CC(\d{5})', branch['name'])
            if tag_code_search:
                tag_code = tag_code_search.group(1)
            else:
                tag_code = 'Falta la palabra CC'
            url = f'https://api.bitbucket.org/2.0/repositories/ficohsacore/{repoName}/diff/{commit_id}'
            response = requests.get(url, headers=headers, auth=oauth)
            content = response.content.decode('ISO-8859-1')
            file_changes = re.findall(r'^diff --git a/(.+?) b/(.+?)$', content, re.MULTILINE)
            for file_change in file_changes:
                data['Repositorio'].append(repoName)
                extension = file_change[0].split('/')[-1].split('.')[-1]  # Obtener la extensión del archivo
                tipo = file_change[0].split('/')[0]  # Obtener el tipo antes del primer "/"
                data['Fecha'].append(fecha_branch.date())  # Usar la fecha extraída del tag o del commit
                if 'user' in branch['target']['author']:
                    data['Autor'].append(branch['target']['author']['user']['display_name'])
                else:
                    data['Autor'].append("Autor desconocido")
                data['CC'].append(tag_code)
                data['Commit'].append(commit_id)
                data['Tipo'].append(tipo)
                data['Ruta base'].append(file_change[0])
                data['Archivo con extensión'].append(file_change[0].split('/')[-1])
                data['Archivo'].append(file_change[0].split('/')[-1].split('.')[0])
                data['Ext'].append(extension)

# Crear un DataFrame con los datos
df = pd.DataFrame(data)


# Crear un archivo de Excel y obtener la hoja de trabajo
wb = Workbook()
ws = wb.active

# Establecer el nombre de la hoja
ws.title = 'Extraccion_' + repoName

# Escribir los encabezados de columna en la primera fila y aplicar estilo
headers = list(df.columns)
for col_num, header in enumerate(headers, 1):
    col_letter = get_column_letter(col_num)
    cell = ws[f'{col_letter}1']
    cell.value = header
    cell.fill = PatternFill(start_color="0068FF", end_color="0068FF", fill_type="solid")  # Azul marino
    cell.font = cell.font.copy(color="FFFFFF")  # Letras blancas

# Escribir los datos en las celdas
for row_num, row_data in enumerate(df.values, 2):
    for col_num, cell_value in enumerate(row_data, 1):
        col_letter = get_column_letter(col_num)
        ws[f'{col_letter}{row_num}'] = cell_value

# Aplicar filtro a los datos
ws.auto_filter.ref = ws.dimensions

# Determinar el prefijo basado en el repoName
prefijos = {

    #Abanks HN
    'Abanks-HN': 'ABANKS_HN',
    'app_abanks_hn': 'ABANKS_HN',
    'electrobank_hn': 'ABANKS_HN',
    'mnu_abanks_hn': 'ABANKS_HN',
    'ncv_hn': 'ABANKS_HN',
    'ora_bank_hn': 'ABANKS_HN',
    'orabanks_hn': 'ABANKS_HN',
    'regionalsim1_hn': 'ABANKS_HN',


    'abanks-pn': 'ABANKS_PA',
    'app_mnu_gt': 'ABANKS_GT',
    'app_abank_gt': 'ABANKS_GT',
    'orabanks_gt': 'ABANKS_GT',
    'ora_bank_gt': 'ABANKS_GT',
    'electrobank_gt': 'ABANKS_GT',
   
    'rutinas-t24-hn': 'T24',
    'omnicanal': 'OMNICANAL',

    #AcselX HN
    'bd_seguroshn_ficohsa': 'ACSELX_HN',
    'acselx-hn': 'ACSELX_HN',
    'bd_seguroshn_analytics': 'ACSELX_HN',
    'bd_seguroshn_bi': 'ACSELX_HN',
    'bd_seguroshn_wsso': 'ACSELX_HN',
    'AcselX-HN-Update': 'ACSELX_HN',

    #Acsel GT
    'bd_segurosgt_ficohsa': 'ACSELX_GT',
    'bd_segurosgt_wsso': 'ACSELX_GT',
    'acselx-gt': 'ACSELX_GT',

    #SYSDE
    'app_pen': 'SYSDE',
    'app_pen_microsit': 'SYSDE',
    'app_pen_sitio_web': 'SYSDE',
    'db_midas': 'SYSDE',
    'db_pen': 'SYSDE',
    'db_pen_apex_report': 'SYSDE',
    'db_pen_api': 'SYSDE',
    'db_pen_mg': 'SYSDE',

    #INXU
    'db_inxu_bansegnic': 'INXU',
    'db_inxu_banseghn': 'INXU',
    'db_inxu_banseggt': 'INXU',
    'app_inxu': 'INXU',

    #Crediforce
    'app_CreditForce': 'CREDIFORCE',
    'DB_CreditForce_GT': 'CREDIFORCE',
    'DB_CreditForce_HN': 'CREDIFORCE',
    'DB_CreditForce_NIC': 'CREDIFORCE',
    'DB_CreditForce_PA': 'CREDIFORCE',

    #CASA DE BOLSA
    'DB_CasaBolsa' : 'CASADEBOLSA',



    #ABANKS PA
    'app_abanks_pa': 'ABANKS_PA',
    'bd_electrobank_pa': 'ABANKS_PA',
    'bd_ora_bank_pa': 'ABANKS_PA',
    'bd_regionalsim1_pa': 'ABANKS_PA',
    'bd_vasa_pa': 'ABANKS_PA',
    'mnu_abanks_pa': 'ABANKS_PA',



}

if repoName == 'mnu-abanks':
    wb.save(f'Merges_ABANKS_HN_{repoName}_{date_time_format}_.xlsx')
    wb.save(f'Merges_ABANKS_PA_{repoName}_{date_time_format}_.xlsx')
else:
    prefijo = prefijos.get(repoName, repoName)
    # Guardar el archivo de Excel
    wb.save(f'Merges_{prefijo}_{repoName}_{date_time_format}__.xlsx')

print("Archivo de Excel generado y guardado con éxito.")





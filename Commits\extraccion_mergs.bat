@echo off
echo Ejecutando extracciones de merges para múltiples repositorios

:: Definir la opción de fecha (1-<PERSON><PERSON>/<PERSON>, 2-<PERSON><PERSON> personalizado, 3-<PERSON><PERSON><PERSON>s 35 dias)
set OPCION=2
set FECHA_INICIO=2024-01-01
set FECHA_FIN=2025-05-01

:: Lista de repositorios a procesar
set REPOSITORIOS=ncv_hn app_proc_hn

:: Procesar cada repositorio
for %%r in (%REPOSITORIOS%) do (
    echo.
    echo Procesando repositorio: %%r
   
    :: Crear archivo de entrada temporal
    >temp_input.txt (
        echo %%r
        echo %OPCION%
        if "%OPCION%"=="1" (
            echo 1
            echo 2023
        ) else if "%OPCION%"=="2" (
            echo %FECHA_INICIO%
            echo %FECHA_FIN%
        )
    )
   
    :: Verificar contenido del archivo temporal (para depuración)
    echo Contenido del archivo de entrada:
    type temp_input.txt
    echo.
   
    :: Ejecutar el script Python
    python Extraccion_merges.py < temp_input.txt
   
    :: Eliminar archivo temporal
    del temp_input.txt
)

echo Todos los scripts completados
pause




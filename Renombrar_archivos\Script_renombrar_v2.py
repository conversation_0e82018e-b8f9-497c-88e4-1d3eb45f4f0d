import requests
import pandas as pd
from io import StringIO
import os
import webbrowser
from urllib.parse import urlparse, parse_qs

def extraer_id_hoja(url):
    """
    Extrae el ID de la hoja de cálculo de Google Sheets desde la URL
    
    Args:
        url (str): URL de Google Sheets
    
    Returns:
        tuple: (spreadsheet_id, gid)
    """
    try:
        # Extraer el ID del spreadsheet
        if "/spreadsheets/d/" in url:
            spreadsheet_id = url.split("/spreadsheets/d/")[1].split("/")[0]
        else:
            return None, None
        
        # Extraer el gid si existe
        parsed_url = urlparse(url)
        query_params = parse_qs(parsed_url.query)
        gid = query_params.get('gid', [None])[0]
        
        return spreadsheet_id, gid
    except:
        return None, None

def crear_enlace_publico(spreadsheet_id, gid=None):
    """
    Crea un enlace público para descargar CSV desde Google Sheets
    
    Args:
        spreadsheet_id (str): ID del spreadsheet
        gid (str): ID de la hoja específica (opcional)
    
    Returns:
        str: URL para descarga pública
    """
    base_url = f"https://docs.google.com/spreadsheets/d/{spreadsheet_id}/export?format=csv"
    if gid:
        base_url += f"&gid={gid}"
    return base_url

def descargar_csv_google_sheets(url, nombre_archivo="datos.csv"):
    """
    Descarga un archivo CSV desde un enlace de Google Sheets
    
    Args:
        url (str): URL del archivo CSV de Google Sheets
        nombre_archivo (str): Nombre del archivo donde guardar los datos
    
    Returns:
        pandas.DataFrame: DataFrame con los datos descargados
    """
    try:
        # Hacer la petición HTTP
        print(f"Descargando datos desde: {url}")
        response = requests.get(url)
        
        # Verificar que la petición fue exitosa
        if response.status_code == 200:
            print("✅ Descarga exitosa!")
            
            # Leer el contenido CSV
            csv_content = response.text
            
            # Convertir a DataFrame
            df = pd.read_csv(StringIO(csv_content))
            
            # Guardar el archivo localmente
            df.to_csv(nombre_archivo, index=False)
            print(f"✅ Archivo guardado como: {nombre_archivo}")
            
            # Mostrar información básica
            print(f"\nInformación del archivo:")
            print(f"- Filas: {len(df)}")
            print(f"- Columnas: {len(df.columns)}")
            print(f"- Columnas disponibles: {list(df.columns)}")
            
            # Mostrar las primeras filas
            print(f"\nPrimeras 5 filas:")
            print(df.head())
            
            return df
            
        elif response.status_code == 401:
            print(f"❌ Error 401: Acceso no autorizado")
            print("El archivo requiere autenticación (no es público)")
            return None
            
        else:
            print(f"❌ Error al descargar: Código {response.status_code}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error de conexión: {e}")
        return None
    except Exception as e:
        print(f"❌ Error inesperado: {e}")
        return None

def leer_csv_local(nombre_archivo):
    """
    Lee un archivo CSV local y lo convierte a DataFrame
    
    Args:
        nombre_archivo (str): Nombre del archivo CSV
    
    Returns:
        pandas.DataFrame: DataFrame con los datos
    """
    try:
        if os.path.exists(nombre_archivo):
            df = pd.read_csv(nombre_archivo)
            print(f"✅ Archivo '{nombre_archivo}' leído exitosamente")
            print(f"- Filas: {len(df)}")
            print(f"- Columnas: {len(df.columns)}")
            print(f"- Columnas disponibles: {list(df.columns)}")
            print(f"\nPrimeras 5 filas:")
            print(df.head())
            return df
        else:
            print(f"❌ El archivo '{nombre_archivo}' no existe")
            return None
    except Exception as e:
        print(f"❌ Error al leer el archivo: {e}")
        return None

def mostrar_opciones_descarga(url):
    """
    Muestra las opciones disponibles para acceder a los datos
    
    Args:
        url (str): URL original de Google Sheets
    """
    spreadsheet_id, gid = extraer_id_hoja(url)
    
    if spreadsheet_id:
        print(f"\n📋 OPCIONES PARA ACCEDER A LOS DATOS:")
        print(f"=" * 50)
        
        print(f"\n1️⃣ HACER EL DOCUMENTO PÚBLICO:")
        print(f"   - Abre: https://docs.google.com/spreadsheets/d/{spreadsheet_id}")
        print(f"   - Haz clic en 'Compartir' → 'Cambiar a cualquiera que tenga el enlace'")
        print(f"   - Luego ejecuta el código nuevamente")
        
        print(f"\n2️⃣ DESCARGAR MANUALMENTE:")
        print(f"   - Abre: https://docs.google.com/spreadsheets/d/{spreadsheet_id}")
        print(f"   - Ve a Archivo → Descargar → Valores separados por comas (.csv)")
        print(f"   - Guarda el archivo como 'datos.csv' en la misma carpeta")
        print(f"   - Ejecuta: leer_csv_local('datos.csv')")
        
        print(f"\n3️⃣ USAR GOOGLE SHEETS API:")
        print(f"   - Requiere configurar credenciales de Google")
        print(f"   - Más complejo pero permite acceso programático")
        
        # Abrir el enlace en el navegador
        enlace_sheets = f"https://docs.google.com/spreadsheets/d/{spreadsheet_id}"
        print(f"\n🌐 Abriendo Google Sheets en tu navegador...")
        try:
            webbrowser.open(enlace_sheets)
        except:
            print(f"   No se pudo abrir automáticamente. Visita: {enlace_sheets}")
    else:
        print("❌ No se pudo extraer información de la URL")

# URL del archivo CSV
url = "https://docs.google.com/spreadsheets/d/10sl4AYV9eoTO_Q9F8YN-wB0Y1xc6QF322b-ZK2241Qc/export?format=csv&gid=1140913444"

# Ejecutar la descarga
if __name__ == "__main__":
    print("🔄 Intentando descargar el archivo CSV...")
    
    # Descargar y procesar el archivo
    datos = descargar_csv_google_sheets(url, "mi_archivo.csv")
    
    if datos is not None:
        print("\n🎉 ¡Descarga completada exitosamente!")
        print("Puedes usar la variable 'datos' para trabajar con el DataFrame")
        
        # Ejemplo de uso adicional
        print("\n--- Ejemplo de análisis básico ---")
        print(f"Información general:")
        print(datos.info())
        
        # Si hay columnas numéricas, mostrar estadísticas
        numericas = datos.select_dtypes(include=['number'])
        if not numericas.empty:
            print(f"\nEstadísticas de columnas numéricas:")
            print(numericas.describe())
    else:
        print("\n📋 El archivo no se pudo descargar automáticamente")
        mostrar_opciones_descarga(url)
        
        # Intentar leer archivo local si existe
        print(f"\n🔍 Buscando archivo local...")
        datos_local = leer_csv_local("mi_archivo.csv")
        if datos_local is not None:
            print("✅ Usando archivo local encontrado")
            datos = datos_local
        else:
            print("❌ No se encontró archivo local")
            
    # Funciones adicionales para trabajar con los datos
    print(f"\n📚 FUNCIONES DISPONIBLES:")
    print(f"- leer_csv_local('nombre_archivo.csv') - Lee un archivo CSV local")
    print(f"- mostrar_opciones_descarga(url) - Muestra opciones para acceder a los datos")
    print(f"- extraer_id_hoja(url) - Extrae IDs de la URL de Google Sheets")
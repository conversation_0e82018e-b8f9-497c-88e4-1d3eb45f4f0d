import os
import requests
from urllib.parse import urlparse, unquote

def leer_enlaces(archivo):
    try:
        with open(archivo, 'r') as file:
            enlaces = file.readlines()
            enlaces = [enlace.strip() for enlace in enlaces if enlace.strip()]
            return enlaces
    except FileNotFoundError:
        print(f"El archivo {archivo} no se encontró.")
        return []
    except Exception as e:
        print(f"Ocurrió un error: {e}")
        return []

def descargar_archivo(url, destino):
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()

        with open(destino, 'wb') as f:
            for chunk in response.iter_content(chunk_size=8192):
                f.write(chunk)
        return True
    except Exception as e:
        print(f"Error al descargar {url}: {e}")
        return False

def obtener_extension(url):
    path = urlparse(url).path
    filename = os.path.basename(path)
    filename = unquote(filename)  # decode percent-encoded string
    if '.' in filename:
        ext = os.path.splitext(filename)[1]
        return ext
    else:
        return ''  # no extension found

def leer_contador(ruta):
    if os.path.exists(ruta):
        with open(ruta, 'r') as f:
            try:
                val = int(f.read().strip())
                return val
            except:
                return 0
    return 0

def guardar_contador(ruta, valor):
    with open(ruta, 'w') as f:
        f.write(str(valor))

def main():
    archivo_enlaces = 'Enlaces.txt'
    folder_descargas = 'downloads'
    contador_file = 'download_counter.txt'

    if not os.path.exists(folder_descargas):
        os.makedirs(folder_descargas)

    enlaces = leer_enlaces(archivo_enlaces)
    if not enlaces:
        print("No hay enlaces para descargar.")
        return

    contador = leer_contador(contador_file)

    for url in enlaces:
        contador += 1
        ext = obtener_extension(url)
        if ext == '':
            ext = '.bin'  # default fallback

        nuevo_nombre = f"archivo_{contador}{ext}"
        destino = os.path.join(folder_descargas, nuevo_nombre)

        print(f"Descargando {url} ...")
        exito = descargar_archivo(url, destino)
        if exito:
            print(f"Descargado y renombrado a {nuevo_nombre}")
        else:
            print(f"Fallo la descarga de {url}, se saltará renombrar.")

    guardar_contador(contador_file, contador)
    print("Proceso completado.")

if __name__ == "__main__":
    main()


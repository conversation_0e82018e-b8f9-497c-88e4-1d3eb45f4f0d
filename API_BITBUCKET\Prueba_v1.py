# import requests
# from requests_oauthlib import OAuth1

# # Tus claves de consumidor
# consumer_key = 'h4MketHQ9gPHv57Cms'
# consumer_secret = 'u6St6a6ARrgS23NjWY97Ps2MW9YP6MBA'

# # Autenticación OAuth1
# oauth = OAuth1(
#     client_key=consumer_key,
#     client_secret=consumer_secret
# )

# # Datos de la nueva rama
# repo_owner = 'ou01696'
# repo_slug = 'pruebas-versionamiento'
# new_branch_name = 'Rama_Prueba_v01'
# #base_commit_hash = 'sha-del-commit-base'

# # URL de la API para crear una nueva rama
# url = f'https://api.bitbucket.org/2.0/repositories/{repo_owner}/{repo_slug}/refs'

# # Datos de la nueva rama en JSON
# data = {
#     'name': new_branch_name,
#     #'target': {
#     #   'hash': base_commit_hash
#     #}
# }

# # Solicitud POST para crear la nueva rama
# response = requests.post(url, json=data, auth=oauth)

# # Verificación de la respuesta
# if response.status_code == 201:
#     print('La nueva rama se ha creado con éxito.')
# else:
#     print(f'Error al crear la rama: {response.status_code}')
#     try:
#         print(response.json())
#     except requests.exceptions.JSONDecodeError:
#         print(response.text)


import requests
from requests.auth import HTTPBasicAuth
from requests_oauthlib import OAuth1

# Información de autenticación
consumer_key = 'h4MketHQ9gPHv57Cms'
consumer_secret = 'u6St6a6ARrgS23NjWY97Ps2MW9YP6MBA'

# Información del repositorio
repo_owner = 'ficohsacore'
repo_slug = 'pruebas_versionamiento'
user_id = 'OU01696'
incident_id = 'BPM-000001'
hotfix_number = 1  # Comenzamos con HF01

# Función para generar el nombre de la rama
def generate_branch_name(user_id, incident_id, hotfix_number):
    return f'hotfix/{user_id}_{incident_id}_HF{str(hotfix_number).zfill(2)}'

# Configuración de OAuth1
auth = OAuth1(consumer_key, consumer_secret)

# URL de la API para obtener el último commit
commits_url = f'https://api.bitbucket.org/2.0/repositories/{repo_owner}/{repo_slug}/commits/master'

# Obtener el hash del último commit
response = requests.get(commits_url, auth=auth)
if response.status_code == 200:
    commit_hash = response.json()['values'][0]['hash']
    print(f'Hash del último commit: {commit_hash}')
else:
    print(f'Error al obtener el hash del commit: {response.status_code}')
    exit(1)

# URL de la API para obtener la lista de ramas
branches_url = f'https://api.bitbucket.org/2.0/repositories/{repo_owner}/{repo_slug}/refs/branches'

# Verificar si la rama ya existe y generar un nuevo nombre si es necesario
while True:
    branch_name = generate_branch_name(user_id, incident_id, hotfix_number)
    print(f'Intentando crear la rama: {branch_name}')
    
    # Obtener la lista de ramas existentes
    response = requests.get(branches_url, auth=auth)
    if response.status_code == 200:
        existing_branches = [branch['name'] for branch in response.json()['values']]
        print(f'Ramas existentes: {existing_branches}')  # Imprimir todas las ramas existentes
        
        if branch_name not in existing_branches:
            break  # La rama no existe, podemos crearla
        else:
            hotfix_number += 1  # Incrementar el número de hotfix
    else:
        print(f'Error al obtener la lista de ramas: {response.status_code}')
        print(f'Contenido de la respuesta: {response.text}')  # Imprimir el contenido de la respuesta
        exit(1)

# Datos para crear la nueva rama
data = {
    'name': branch_name,
    'target': {
        'hash': commit_hash
    }
}

# Imprimir la URL y los datos antes de la solicitud
print(f'URL para crear la rama: {branches_url}')
print(f'Datos para crear la rama: {data}')

# Realizar la petición POST para crear la rama
response = requests.post(branches_url, json=data, auth=auth)

# Verificar el resultado
if response.status_code == 201:
    print(f'Rama creada exitosamente: {branch_name}')
else:
    print(f'Error al crear la rama: {response.status_code}')
    print(f'Contenido de la respuesta: {response.text}')  # Imprimir el contenido de la respuesta